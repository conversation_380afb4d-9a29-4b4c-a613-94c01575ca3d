from __future__ import annotations

import contextlib
import logging
from dataclasses import dataclass
from typing import Any, cast, TYPE_CHECKING, Dict, Optional
import json
import uuid

from python_lab.models.python_query import PythonQuery

if TYPE_CHECKING:
    from superset.connectors.sqla.models import Database

logger = logging.getLogger(__name__)

PythonResults = dict[str, Any]


@dataclass
class PythonExecutionContext:  # pylint: disable=too-many-instance-attributes
    """Context for Python Lab execution - mirrors SqlJsonExecutionContext"""

    database_id: int
    catalog: str | None
    schema: str
    python_code: str
    template_params: dict[str, Any]
    async_flag: bool
    limit: int
    status: str
    client_id: str
    client_id_or_short_id: str
    python_editor_id: str
    tab_name: str
    user_id: int | None
    expand_data: bool
    create_table_as_select: CreateTableAsSelect | None
    database: Database | None
    query: PythonQuery
    _python_result: PythonResults | None

    def __init__(self, query_params: dict[str, Any]):
        self.create_table_as_select = None
        self.database = None
        self._init_from_query_params(query_params)
        # TODO: Get actual user ID from session/auth
        self.user_id = 1  # Placeholder - should be from authentication
        self.client_id_or_short_id = cast(str, self.client_id or str(uuid.uuid4())[:10])

    def set_query(self, query: PythonQuery) -> None:
        self.query = query

    def _init_from_query_params(self, query_params: dict[str, Any]) -> None:
        self.database_id = cast(int, query_params.get("database_id"))
        self.catalog = cast(str, query_params.get("catalog"))
        self.schema = cast(str, query_params.get("schema"))
        self.python_code = cast(str, query_params.get("python"))
        self.template_params = self._get_template_params(query_params)
        self.async_flag = cast(bool, query_params.get("runAsync", False))
        self.limit = self._get_limit_param(query_params)
        self.status = cast(str, query_params.get("status", "pending"))
        if cast(bool, query_params.get("select_as_cta")):
            self.create_table_as_select = CreateTableAsSelect.create_from(query_params)
        self.client_id = cast(str, query_params.get("client_id"))
        self.python_editor_id = cast(str, query_params.get("python_editor_id"))
        self.tab_name = cast(str, query_params.get("tab"))
        self.expand_data: bool = cast(bool, query_params.get("expand_data", False))

    @staticmethod
    def _get_template_params(query_params: dict[str, Any]) -> dict[str, Any]:
        try:
            template_params = json.loads(query_params.get("templateParams") or "{}")
        except json.JSONDecodeError:
            logger.warning(
                "Invalid template parameter %s specified. Defaulting to empty dict",
                str(query_params.get("templateParams")),
            )
            template_params = {}
        return template_params

    @staticmethod
    def _get_limit_param(query_params: dict[str, Any]) -> int:
        limit = query_params.get("queryLimit") or 1000000
        if limit < 0:
            logger.warning("Invalid limit of %i specified. Defaulting to 1000.", limit)
            limit = 1000000
        return limit

    def is_run_asynchronous(self) -> bool:
        return self.async_flag

    @property
    def select_as_cta(self) -> bool:
        return self.create_table_as_select is not None

    def set_database(self, database: Database) -> None:
        self._validate_db(database)
        self.database = database
        if self.catalog is None:
            self.catalog = database.get_default_catalog()
        if self.select_as_cta:
            schema_name = self._get_ctas_target_schema_name(database)
            self.create_table_as_select.target_schema_name = schema_name  # type: ignore

    def _get_ctas_target_schema_name(self, database: Database) -> str | None:
        if database.force_ctas_schema:
            return database.force_ctas_schema
        # For Python Lab, we'll use the provided schema or default
        return self.schema

    def _validate_db(self, database: Database) -> None:
        # TODO: validate db.id is equal to self.database_id
        pass

    def get_execution_result(self) -> PythonResults | None:
        return self._python_result

    def set_execution_result(self, python_result: PythonResults | None) -> None:
        self._python_result = python_result

    def create_query(self) -> PythonQuery:
        from datetime import datetime

        start_time = datetime.now().timestamp()

        if self.select_as_cta:
            return PythonQuery(
                database_id=self.database_id,
                python_code=self.python_code,
                catalog=self.catalog,
                schema=self.schema,
                select_as_cta=True,
                ctas_method=self.create_table_as_select.ctas_method,  # type: ignore
                start_time=start_time,
                tab_name=self.tab_name,
                status=self.status,
                limit=self.limit,
                python_editor_id=self.python_editor_id,
                tmp_table_name=self.create_table_as_select.target_table_name,  # type: ignore
                tmp_schema_name=self.create_table_as_select.target_schema_name,  # type: ignore
                user_id=self.user_id,
                client_id=self.client_id_or_short_id,
                template_params=self.template_params,
            )
        return PythonQuery(
            database_id=self.database_id,
            python_code=self.python_code,
            catalog=self.catalog,
            schema=self.schema,
            select_as_cta=False,
            start_time=start_time,
            tab_name=self.tab_name,
            limit=self.limit,
            status=self.status,
            python_editor_id=self.python_editor_id,
            user_id=self.user_id,
            client_id=self.client_id_or_short_id,
            template_params=self.template_params,
        )

    def get_query_details(self) -> str:
        with contextlib.suppress(Exception):
            if hasattr(self, "query"):
                if self.query.id:
                    return f"python query '{self.query.id}' - '{self.query.python_code[:100]}...'"
        return f"python query '{self.python_code[:100]}...'"


class CreateTableAsSelect:  # pylint: disable=too-few-public-methods
    """Handle Create Table As Select operations for Python Lab"""

    ctas_method: str
    target_schema_name: str | None
    target_table_name: str

    def __init__(
        self, ctas_method: str, target_schema_name: str, target_table_name: str
    ):
        self.ctas_method = ctas_method
        self.target_schema_name = target_schema_name
        self.target_table_name = target_table_name

    @staticmethod
    def create_from(query_params: dict[str, Any]) -> CreateTableAsSelect:
        ctas_method = query_params.get("ctas_method", "TABLE")
        schema = cast(str, query_params.get("schema"))
        tmp_table_name = cast(str, query_params.get("tmp_table_name"))
        return CreateTableAsSelect(ctas_method, schema, tmp_table_name)
