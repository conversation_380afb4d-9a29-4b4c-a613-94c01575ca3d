from flask import Blueprint, request, jsonify, Response, g
from datetime import datetime, timedelta
import uuid
import pandas as pd
import json
import time
import traceback
import logging
from typing import Dict, Any, Optional
from marshmallow import ValidationError
import urllib.parse

from utils.common import superset_connection, bootstrap_python_lab_data
from python_lab.lab.python_validator import PythonValidator
from python_lab.lab.python_executor import PythonExecutor
from python_lab.lab.schemas import (
    estimate_python_cost_schema,
    execute_python_payload_schema,
    format_python_payload_schema,
    validate_python_payload_schema,
    save_python_query_schema,
    save_dataset_schema,
    python_lab_get_results_schema,
)
from python_lab.models.python_query import PythonQuery

# Configure logging
logger = logging.getLogger(__name__)

python_lab_bp = Blueprint("python_lab_bp", __name__)

# Global results storage - In production, use Redis or database
results_storage = {}
RESULT_EXPIRY_HOURS = 24

# Global variable to store the correct password (for testing)
CORRECT_DB_PASSWORD = None


def get_superset_db_config():
    """Get Superset database configuration from environment/config"""
    import os

    # Check if we're in Docker environment
    is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
    host = "db" if is_docker else "localhost"

    # Try to detect Docker environment automatically
    if not is_docker:
        try:
            # Check if we can connect to 'db' host (Docker)
            import socket

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(("db", 5432))
            sock.close()
            if result == 0:
                host = "db"
        except:
            pass

    return {
        "dbname": "superset",
        "user": "superset",
        "password": "superset",
        "host": host,
        "port": 5432,
    }


def get_database_password_replacement():
    """Get the password to replace XXXXXXXXXX with"""
    global CORRECT_DB_PASSWORD

    # If manually set, use that
    if CORRECT_DB_PASSWORD:
        return CORRECT_DB_PASSWORD

    # Try different approaches to get the correct password
    import os

    # Option 1: Environment variable
    password = os.getenv("SUPERSET_DB_PASSWORD")
    if password:
        return password

    # Option 2: Try common passwords (empty string is often used in Docker)
    return ""  # Try empty string first


def get_user_id():
    """Get current user ID from session/auth"""
    # TODO: Implement proper authentication integration
    if hasattr(g, "user") and g.user:
        return g.user.id
    return 1  # Placeholder - MUST be replaced with real auth


def validate_request_json(schema, data=None):
    """Helper function to validate JSON requests using marshmallow schemas"""
    try:
        if data is None:
            data = request.json

        if not data:
            return None, {"error": "No JSON payload provided"}, 400

        validated_data = schema.load(data)
        return validated_data, None, None

    except ValidationError as e:
        return None, {"error": "Validation failed", "details": e.messages}, 400
    except Exception as e:
        return None, {"error": "Invalid JSON", "details": str(e)}, 400


def handle_database_error(func):
    """Decorator for handling database errors"""

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database error in {func.__name__}: {e}")
            superset_connection.rollback()
            return jsonify({"error": "Database error occurred", "details": str(e)}), 500

    return wrapper


def store_results(data: pd.DataFrame, query_id: Optional[int] = None) -> str:
    """Store execution results and return storage key"""
    results_key = str(uuid.uuid4())
    expiry_time = datetime.now() + timedelta(hours=RESULT_EXPIRY_HOURS)

    results_storage[results_key] = {
        "data": data,
        "query_id": query_id,
        "created_at": datetime.now(),
        "expires_at": expiry_time,
    }

    # Clean up expired results
    cleanup_expired_results()

    return results_key


def cleanup_expired_results():
    """Remove expired results from storage"""
    now = datetime.now()
    expired_keys = [
        key
        for key, value in results_storage.items()
        if value.get("expires_at", now) < now
    ]
    for key in expired_keys:
        del results_storage[key]


def get_database_uri_by_id(db_id: int) -> Optional[str]:
    """Get database URI by database ID"""
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute("SELECT sqlalchemy_uri FROM dbs WHERE id = %s", (db_id,))
            result = cursor.fetchone()
            if result:
                uri = result[0]
                # Replace XXXXXXXXXX with actual password
                if "XXXXXXXXXX" in uri:
                    password = get_database_password_replacement()
                    uri = uri.replace("XXXXXXXXXX", password)
                return uri
            return None
    except Exception as e:
        logger.error(f"Error fetching database URI by ID: {e}")
        return None


def get_database_uri_by_name(db_name: str) -> Optional[str]:
    """Get database URI by database name"""
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(
                "SELECT sqlalchemy_uri FROM dbs WHERE database_name = %s", (db_name,)
            )
            result = cursor.fetchone()
            if result:
                uri = result[0]
                # Replace XXXXXXXXXX with actual password
                if "XXXXXXXXXX" in uri:
                    password = get_database_password_replacement()
                    uri = uri.replace("XXXXXXXXXX", password)
                return uri
            return None
    except Exception as e:
        logger.error(f"Error fetching database URI by name: {e}")
        return None


def json_success(payload, status=200):
    """Helper to return JSON success response"""
    response = Response(payload, status=status, mimetype="application/json")
    return response


# ============================================================================
# MAIN API ENDPOINTS - MIRRORING SQL LAB
# ============================================================================


@python_lab_bp.route("/", methods=["GET"])
def get_bootstrap_data():
    """
    Get the bootstrap data for Python Lab page - mirrors SQL Lab
    """
    try:
        user_id = get_user_id()
        result = bootstrap_python_lab_data(user_id)

        return json_success(
            json.dumps({"result": result}, default=str, ignore_nan=True)
        )

    except Exception as e:
        logger.error(f"Failed to get bootstrap data: {e}")
        return (
            jsonify({"error": "Failed to get bootstrap data", "details": str(e)}),
            500,
        )


@python_lab_bp.route("/estimate/", methods=["POST"])
def estimate_query_cost():
    """Estimate the Python code execution cost"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            estimate_python_cost_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        python_code = validated_data.get("python", "")

        # Simple cost estimation
        lines = python_code.split("\n")
        non_empty_lines = [line.strip() for line in lines if line.strip()]

        complexity_score = len(non_empty_lines)

        # Add complexity for certain operations
        for line in non_empty_lines:
            if "for " in line or "while " in line:
                complexity_score += 5
            if "read_sql" in line or "to_sql" in line:
                complexity_score += 10
            if "merge" in line or "join" in line:
                complexity_score += 3

        cost_estimate = [
            {
                "complexity_score": complexity_score,
                "estimated_time": f"{complexity_score * 0.1:.1f} seconds",
                "lines_of_code": len(non_empty_lines),
            }
        ]

        return jsonify({"result": cost_estimate}), 200

    except Exception as e:
        logger.error(f"Cost estimation failed: {e}")
        return jsonify({"error": "Estimation failed", "details": str(e)}), 500


@python_lab_bp.route("/format_sql/", methods=["POST"])
def format_python():
    """Format Python code"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            format_python_payload_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        python_code = validated_data["python"]

        # Basic formatting
        try:
            lines = python_code.split("\n")
            formatted_lines = []
            indent_level = 0

            for line in lines:
                stripped = line.strip()
                if not stripped:
                    formatted_lines.append("")
                    continue

                # Basic indentation logic
                if stripped.startswith(("except", "elif", "else", "finally")):
                    current_indent = max(0, indent_level - 1)
                else:
                    current_indent = indent_level

                formatted_lines.append("    " * current_indent + stripped)

                if stripped.endswith(":"):
                    indent_level += 1

            formatted_code = "\n".join(formatted_lines)
        except:
            formatted_code = python_code  # Return original if formatting fails

        return jsonify({"result": formatted_code}), 200

    except Exception as e:
        logger.error(f"Code formatting failed: {e}")
        return jsonify({"error": "Formatting failed", "details": str(e)}), 500


@python_lab_bp.route("/export/<string:client_id>/")
def export_csv(client_id: str):
    """Export the Python query results to a CSV"""
    try:
        # Validate client_id format
        if not client_id or len(client_id) > 255:
            return jsonify({"error": "Invalid client_id"}), 400

        # Get query by client_id
        with superset_connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, tab_name, results_key, user_id 
                FROM python_lab_query 
                WHERE client_id = %s
            """,
                (client_id,),
            )

            result = cursor.fetchone()
            if not result:
                return (
                    jsonify({"error": f"Query with client_id {client_id} not found"}),
                    404,
                )

            query_id, tab_name, results_key, query_user_id = result

        # Check if user has access to this query
        current_user_id = get_user_id()
        if query_user_id != current_user_id:
            return jsonify({"error": "Access denied"}), 403

        # Get results from storage
        if not results_key or results_key not in results_storage:
            return jsonify({"error": "Results not found or expired"}), 410

        stored_result = results_storage[results_key]
        df = stored_result["data"]

        if not isinstance(df, pd.DataFrame):
            return jsonify({"error": "Invalid result data format"}), 500

        # Generate CSV
        csv_data = df.to_csv(index=False)

        # Create safe filename
        safe_filename = "".join(
            c
            for c in (tab_name or "python_query")
            if c.isalnum() or c in (" ", "-", "_")
        ).rstrip()
        if not safe_filename:
            safe_filename = "python_query"

        # Create response
        response = Response(
            csv_data,
            mimetype="text/csv",
            headers={
                "Content-Disposition": f'attachment; filename="{safe_filename}.csv"',
                "Content-Type": "text/csv; charset=utf-8",
            },
        )

        return response

    except Exception as e:
        logger.error(f"CSV export failed: {e}")
        return jsonify({"error": "Export failed", "details": str(e)}), 500


@python_lab_bp.route("/results/", methods=["POST", "GET"])
def get_results():
    """Get the result of a Python query execution"""
    try:
        # Parse query parameter (support both rison and JSON)
        # query_data = request.args.get("q")
        query_data = request.json or query_data  # Support both JSON and rison formats
        if not query_data:
            return jsonify({"error": "Missing query parameter 'q'"}), 400

        # try:
        #     # Try JSON first
        #     params = json.loads(urllib.parse.unquote(query_data))
        # except:
        #     return jsonify({"error": "Invalid query parameter format"}), 400

        # Validate parameters
        params = query_data or {}  # Support both JSON and rison formats (JSON takes precedence)  # noqa: E704
        key = params.get("key")
        rows = params.get("rows")

        if not key:
            return jsonify({"error": "Missing 'key' parameter"}), 400

        # Get results from storage
        if key not in results_storage:
            return jsonify({"error": "Results not found or expired"}), 410

        stored_result = results_storage[key]
        result_data = stored_result["data"]

        # Check if results have expired
        if stored_result.get("expires_at", datetime.now()) < datetime.now():
            del results_storage[key]
            return jsonify({"error": "Results have expired"}), 410

        # Apply row limit if specified
        display_limit_reached = False
        if rows and isinstance(result_data, pd.DataFrame):
            if len(result_data) > rows:
                result_data = result_data.head(rows)
                display_limit_reached = True

        # Custom function to handle NaN values
        def handle_nan_values(obj):
            if isinstance(obj, float) and (pd.isna(obj) or obj != obj):  # Check for NaN
                return None
            return str(obj)

        # Format response to match SQL Lab
        if isinstance(result_data, pd.DataFrame):
            # Convert DataFrame to list and handle NaN values
            data_list = []
            for row in result_data.values:
                processed_row = []
                for value in row:
                    if pd.isna(value):
                        processed_row.append(None)
                    else:
                        processed_row.append(value)
                data_list.append(processed_row)
            
            payload = json.dumps(
                {
                    "query": {
                        "id": stored_result.get("query_id"),
                        "status": "success",
                        "rows": len(result_data),
                        "columns": [
                            {
                                "name": col,
                                "type": str(result_data[col].dtype),
                                "is_dttm": str(result_data[col].dtype).startswith(
                                    "datetime"
                                ),
                            }
                            for col in result_data.columns
                        ],
                    },
                    "data": data_list,
                    "displayLimitReached": display_limit_reached,
                },
                default=handle_nan_values,
            )
        else:
            payload = json.dumps(
                {
                    "query": {
                        "id": stored_result.get("query_id"),
                        "status": "success",
                        "rows": 0,
                        "columns": [],
                    },
                    "data": [],
                    "message": "No data returned",
                },
                default=handle_nan_values,
            )

        return json_success(payload)

    except Exception as e:
        logger.error(f"Failed to get results: {e}")
        return jsonify({"error": "Failed to get results", "details": str(e)}), 500

# Replace the execute_python_query function in your routers.py with this updated version


@python_lab_bp.route("/execute", methods=["POST"])
def execute_python_query():
    """Execute a Python query - RETURNS DETAILED ERROR FORMAT"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            execute_python_payload_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        # Extract parameters
        python_code = validated_data.get("python")
        database_id = validated_data.get("database_id")
        database_name = validated_data.get("database_name")
        runAsync = validated_data.get("runAsync", False)
        client_id = validated_data.get("client_id", str(uuid.uuid4())[:10])
        tab_name = validated_data.get("tab", "Python Query")

        # Create executor
        superset_db_config = get_superset_db_config()
        executor = PythonExecutor(superset_db_config)

        # Save query record
        query_id = None
        try:
            query_id = save_simple_query_record(
                client_id=client_id,
                user_id=get_user_id(),
                database_id=database_id,
                python_code=python_code,
                tab_name=tab_name,
            )
        except Exception as e:
            logger.warning(f"Failed to save query record: {e}")

        if runAsync:
            # For async, return immediately
            if query_id:
                update_query_status(query_id, "running", 0)

            response = {
                "query_id": query_id,
                "status": "running",
                "query": {
                    "id": query_id,
                    "queryId": query_id,
                    "status": "running",
                    "state": "running",
                    "progress": 0
                }
            }
            return jsonify(response), 202
        else:
            # Synchronous execution
            if query_id:
                update_query_status(query_id, "running", 0)

            # Execute the Python code
            if database_id:
                result = executor.execute(python_code, db_id=database_id, output_format="sqllab")
            elif database_name:
                result = executor.execute(python_code, db_name=database_name, output_format="sqllab")
            else:
                result = executor.execute(python_code, output_format="sqllab")

            # Set query ID in result
            result.query_id = query_id

            if result.success:
                # Store results if we have data
                results_key = None
                rows_count = 0

                if result.data is not None and not result.data.empty:
                    results_key = store_results(result.data, query_id)
                    rows_count = len(result.data)

                    if query_id:
                        update_query_status(
                            query_id, "success", 100, rows_count, results_key
                        )
                else:
                    if query_id:
                        update_query_status(query_id, "success", 100)

                # Get the SQL Lab format response
                response = result.to_dict(
                    python_code=python_code,
                    database_id=database_id, 
                    tab_name=tab_name,
                    user_id=get_user_id(),
                    results_key=results_key
                )

                return jsonify(response), 200
            else:
                # Execution failed - return detailed error format
                if query_id:
                    update_query_status(
                        query_id, "failed", 0, error_message=result.error
                    )

                # Create detailed error response
                detailed_errors = create_detailed_error_response(
                    result.error, 
                    python_code, 
                    result.traceback_str
                )

                error_response = {
                    "errors": detailed_errors
                }

                return jsonify(error_response), 500

    except Exception as e:
        logger.error(f"Python execution failed: {e}")
        
        # Create detailed error for unexpected failures
        detailed_errors = create_detailed_error_response(
            str(e), 
            python_code if 'python_code' in locals() else "", 
            traceback.format_exc()
        )
        
        error_response = {
            "errors": detailed_errors
        }
        return jsonify(error_response), 500


def create_detailed_error_response(error_message, python_code, traceback_str=None):
    """Create detailed error response matching Superset format"""
    errors = []
    
    # Analyze the error type
    if "SyntaxError" in error_message or "syntax error" in error_message.lower():
        # Syntax error
        line_number, column_number = extract_error_position(error_message, traceback_str)
        
        errors.append({
            "message": f"Error parsing near '{extract_error_token(error_message)}' at line {line_number}:{column_number}",
            "error_type": "INVALID_SQL_ERROR",
            "level": "error",
            "extra": {
                "sql": python_code,
                "engine": "python",
                "line": line_number,
                "column": column_number,
                "issue_codes": [
                    {
                        "code": 1003,
                        "message": "Issue 1003 - There is a syntax error in the Python code. Perhaps there was a misspelling or a typo."
                    }
                ]
            }
        })
    
    elif "not allowed" in error_message.lower() or "dangerous" in error_message.lower():
        # Security/permission error
        errors.append({
            "message": "This operation is not allowed for security reasons. Please contact your administrator for more assistance.",
            "error_type": "DML_NOT_ALLOWED_ERROR", 
            "level": "error",
            "extra": {
                "issue_codes": [
                    {
                        "code": 1022,
                        "message": "Issue 1022 - Operation does not allow potentially dangerous code execution."
                    }
                ]
            }
        })
    
    elif "timeout" in error_message.lower():
        # Timeout error
        errors.append({
            "message": "The query execution timed out. Please try to optimize your code or contact your administrator.",
            "error_type": "QUERY_TIMEOUT_ERROR",
            "level": "error", 
            "extra": {
                "issue_codes": [
                    {
                        "code": 1011,
                        "message": "Issue 1011 - The query might be too complex to run under the time limit."
                    }
                ]
            }
        })
    
    elif "database" in error_message.lower() and ("not found" in error_message.lower() or "connection" in error_message.lower()):
        # Database connection error
        errors.append({
            "message": "Database connection failed. Please check your database configuration.",
            "error_type": "DATABASE_CONNECTION_ERROR",
            "level": "error",
            "extra": {
                "issue_codes": [
                    {
                        "code": 1009,
                        "message": "Issue 1009 - The database connection could not be established."
                    }
                ]
            }
        })
    
    elif "table" in error_message.lower() and ("not found" in error_message.lower() or "does not exist" in error_message.lower()):
        # Table not found error
        table_name = extract_table_name(error_message)
        errors.append({
            "message": f"Table [{table_name}] could not be found, please double check your database connection, schema, and table name.",
            "error_type": "TABLE_NOT_FOUND_ERROR",
            "level": "error",
            "extra": {
                "sql": python_code,
                "engine": "python",
                "issue_codes": [
                    {
                        "code": 1015,
                        "message": "Issue 1015 - The table was deleted or renamed in the database."
                    }
                ]
            }
        })
    
    else:
        # Generic Python execution error
        errors.append({
            "message": error_message,
            "error_type": "PYTHON_EXECUTION_ERROR",
            "level": "error",
            "extra": {
                "sql": python_code,
                "engine": "python",
                "issue_codes": [
                    {
                        "code": 1001,
                        "message": "Issue 1001 - Python code execution failed due to an unexpected error."
                    }
                ]
            }
        })
    
    return errors


def extract_error_position(error_message, traceback_str):
    """Extract line and column numbers from error message"""
    import re
    
    # Try to extract line number from traceback or error message
    line_match = re.search(r'line (\d+)', error_message)
    if line_match:
        line_number = int(line_match.group(1))
    else:
        line_number = 1
    
    # Try to extract column number
    col_match = re.search(r'column (\d+)', error_message)
    if col_match:
        column_number = int(col_match.group(1))
    else:
        column_number = 1
    
    return line_number, column_number


def extract_error_token(error_message):
    """Extract the problematic token from error message"""
    import re
    
    # Look for quoted tokens in error message
    token_match = re.search(r"'([^']+)'", error_message)
    if token_match:
        return token_match.group(1)
    
    # Look for common Python keywords that might cause issues
    common_tokens = ['import', 'def', 'class', 'if', 'for', 'while', 'try', 'except', 'as', 'from']
    for token in common_tokens:
        if token in error_message.lower():
            return token
    
    return "unknown"


def extract_table_name(error_message):
    """Extract table name from error message"""
    import re
    
    # Look for table name in quotes
    table_match = re.search(r'"([^"]+)"', error_message)
    if table_match:
        return table_match.group(1)
    
    # Look for relation name pattern
    relation_match = re.search(r'relation "([^"]+)"', error_message)
    if relation_match:
        return relation_match.group(1)
    
    return "unknown_table"

# Also add this new endpoint for legacy format if needed
@python_lab_bp.route("/execute_legacy/", methods=["POST"])
def execute_python_query_legacy():
    """Execute a Python query - RETURNS LEGACY FORMAT"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            execute_python_payload_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        # Extract parameters
        python_code = validated_data.get("python")
        database_id = validated_data.get("database_id")
        database_name = validated_data.get("database_name")
        runAsync = validated_data.get("runAsync", False)
        client_id = validated_data.get("client_id", str(uuid.uuid4())[:10])
        tab_name = validated_data.get("tab", "Python Query")

        # Create executor
        superset_db_config = get_superset_db_config()
        executor = PythonExecutor(superset_db_config)

        # Save query record
        query_id = None
        try:
            query_id = save_simple_query_record(
                client_id=client_id,
                user_id=get_user_id(),
                database_id=database_id,
                python_code=python_code,
                tab_name=tab_name,
            )
        except Exception as e:
            logger.warning(f"Failed to save query record: {e}")

        if runAsync:
            # For async, return immediately
            if query_id:
                update_query_status(query_id, "running", 0)

            response = {"query": {"id": query_id, "status": "running", "progress": 0}}
            return jsonify(response), 202
        else:
            # Synchronous execution
            if query_id:
                update_query_status(query_id, "running", 0)

            # Execute the Python code with legacy format
            if database_id:
                result = executor.execute(
                    python_code, db_id=database_id, output_format="legacy"
                )
            elif database_name:
                result = executor.execute(
                    python_code, db_name=database_name, output_format="legacy"
                )
            else:
                result = executor.execute(python_code, output_format="legacy")

            # Set query ID in result
            result.query_id = query_id

            if result.success:
                # Store results if we have data
                results_key = None
                rows_count = 0

                if result.data is not None and not result.data.empty:
                    results_key = store_results(result.data, query_id)
                    rows_count = len(result.data)

                    if query_id:
                        update_query_status(
                            query_id, "success", 100, rows_count, results_key
                        )
                else:
                    if query_id:
                        update_query_status(query_id, "success", 100)

                # Format legacy response
                response = result.to_dict_legacy()
                response["query"]["executedSql"] = python_code

                return jsonify(response), 200
            else:
                # Execution failed
                if query_id:
                    update_query_status(
                        query_id, "failed", 0, error_message=result.error
                    )

                return (
                    jsonify(
                        {
                            "errors": [
                                {
                                    "message": result.error or "Unknown error",
                                    "error_type": "PYTHON_EXECUTION_ERROR",
                                }
                            ],
                            "success": False,
                        }
                    ),
                    500,
                )

    except Exception as e:
        logger.error(f"Python execution failed: {e}")
        return (
            jsonify(
                {
                    "errors": [
                        {
                            "message": f"Execution failed: {str(e)}",
                            "error_type": "PYTHON_EXECUTION_ERROR",
                        }
                    ]
                }
            ),
            500,
        )


def save_simple_query_record(
    client_id: str, user_id: int, database_id: int, python_code: str, tab_name: str
) -> int:
    """Save a simplified query record"""
    try:
        with superset_connection.cursor() as cursor:
            insert_query = """
            INSERT INTO python_lab_query (
                client_id, user_id, database_id, status, tab_name, python_code,
                start_time, progress, created_on, changed_on
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) RETURNING id;
            """

            now = datetime.now()

            cursor.execute(
                insert_query,
                (
                    client_id,
                    user_id,
                    database_id,
                    "pending",
                    tab_name,
                    python_code,
                    now.timestamp(),
                    0,
                    now,
                    now,
                ),
            )

            query_id = cursor.fetchone()[0]
            superset_connection.commit()
            return query_id
    except Exception as e:
        logger.error(f"Failed to save simple query record: {e}")
        superset_connection.rollback()
        return None


@handle_database_error
def update_query_status(
    query_id: int,
    status: str,
    progress: int,
    rows: int = 0,
    results_key: Optional[str] = None,
    error_message: Optional[str] = None,
):
    """Update query status in database"""
    with superset_connection.cursor() as cursor:
        update_query = """
        UPDATE python_lab_query 
        SET status = %s, progress = %s, rows = %s, results_key = %s, 
            error_message = %s, end_time = %s, changed_on = %s
        WHERE id = %s
        """

        end_time = (
            datetime.now().timestamp() if status in ["success", "failed"] else None
        )

        cursor.execute(
            update_query,
            (
                status,
                progress,
                rows,
                results_key,
                error_message,
                end_time,
                datetime.now(),
                query_id,
            ),
        )
        superset_connection.commit()


# ============================================================================
# QUERY MANAGEMENT ENDPOINTS
# ============================================================================


@python_lab_bp.route("/save_query", methods=["POST"])
def save_query():
    """Save a Python query as a saved tab"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            save_python_query_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        user_id = get_user_id()
        record_uuid = str(uuid.uuid4())
        current_time = datetime.now()

        # Insert into saved_python_query table
        with superset_connection.cursor() as cursor:
            insert_query = """
            INSERT INTO saved_python_query (
                created_on, changed_on, user_id, db_id, label,
                description, changed_by_fk, created_by_fk, python,
                uuid, catalog
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id;
            """

            cursor.execute(
                insert_query,
                (
                    current_time,
                    current_time,
                    user_id,
                    validated_data["db_id"],
                    validated_data["label"],
                    validated_data["description"],
                    user_id,
                    user_id,
                    validated_data["python"],
                    record_uuid,
                    validated_data.get("catalog"),
                ),
            )

            record_id = cursor.fetchone()[0]
            superset_connection.commit()

        return (
            jsonify(
                {
                    "success": True,
                    "message": "Tab created successfully",
                    "data": {
                        "id": record_id,
                        "uuid": record_uuid,
                        "catalog": validated_data.get("catalog"),
                        "db_id": validated_data["db_id"],
                        "description": validated_data["description"],
                        "label": validated_data["label"],
                        "python": validated_data["python"],
                        "created_on": current_time.isoformat(),
                        "changed_on": current_time.isoformat(),
                    },
                }
            ),
            201,
        )

    except Exception as e:
        logger.error(f"Failed to save query: {e}")
        superset_connection.rollback()
        return jsonify({"error": "Database error occurred", "details": str(e)}), 500


@python_lab_bp.route("/validate", methods=["POST"])
def validate_python():
    """Validate Python code syntax and security"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            validate_python_payload_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        python_code = validated_data["python"]

        # Use validator
        validator = PythonValidator()
        validation_results = validator.validate(python_code)

        # Convert to dict format
        result = [annotation.to_dict() for annotation in validation_results]

        return jsonify({"success": True, "result": result}), 200

    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return jsonify({"error": "Validation error occurred", "details": str(e)}), 500


@python_lab_bp.route("/save_dataset", methods=["POST"])
def save_dataset():
    """Save execution result as a new dataset/table"""
    try:
        # Validate request
        validated_data, error_response, status_code = validate_request_json(
            save_dataset_schema
        )
        if error_response:
            return jsonify(error_response), status_code

        python_code = validated_data["python"]
        db_id = validated_data["db_id"]
        table_name = validated_data["table_name"]
        schema = validated_data.get("schema")
        if_exists = validated_data.get("if_exists", "fail")

        # Create executor and execute code
        superset_db_config = get_superset_db_config()
        executor = PythonExecutor(superset_db_config)
        result = executor.execute(python_code, db_id=db_id)

        if not result.success or result.data is None:
            return (
                jsonify(
                    {
                        "error": "Failed to execute Python code",
                        "details": result.error or "No DataFrame result found",
                    }
                ),
                500,
            )

        # Get target database URI
        target_db_uri = get_database_uri_by_id(db_id)
        if not target_db_uri:
            return jsonify({"error": f"Database with id {db_id} not found"}), 404

        # Save DataFrame to database
        from sqlalchemy import create_engine

        target_engine = create_engine(target_db_uri)

        result.data.to_sql(
            name=table_name,
            con=target_engine,
            schema=schema,
            if_exists=if_exists,
            index=False,
        )

        full_table_name = f"{schema}.{table_name}" if schema else table_name

        return (
            jsonify(
                {
                    "success": True,
                    "table_name": full_table_name,
                    "rows": len(result.data),
                    "message": "Dataset created successfully",
                }
            ),
            201,
        )

    except Exception as e:
        logger.error(f"Failed to save dataset: {e}")
        return jsonify({"error": "Failed to save dataset", "details": str(e)}), 500


# ============================================================================
# DATABASE METADATA ENDPOINTS
# ============================================================================


@python_lab_bp.route("/databases", methods=["GET"])
def get_databases():
    """Get list of databases available for Python Lab"""
    try:
        databases = []

        with superset_connection.cursor() as cursor:
            # Check what columns exist first
            cursor.execute(
                """
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'dbs' AND table_schema = 'public'
            """
            )

            available_columns = [row[0] for row in cursor.fetchall()]

            # Build query with only existing columns
            base_columns = ["id", "database_name", "sqlalchemy_uri", "expose_in_sqllab"]
            optional_columns = [
                "allow_ctas",
                "allow_cvas",
                "allow_dml",
                "allow_run_async",
                "allow_file_upload",
                "allows_subquery",
                "force_ctas_schema",
                "disable_data_preview",
                "disable_drill_to_detail",
                "allow_multi_catalog",
            ]

            # Only include columns that exist
            columns_to_select = base_columns.copy()
            for col in optional_columns:
                if col in available_columns:
                    columns_to_select.append(col)

            query = f"""
                SELECT {', '.join(columns_to_select)}
                FROM dbs 
                WHERE expose_in_sqllab = true
                ORDER BY database_name
            """

            cursor.execute(query)

            for row in cursor.fetchall():
                # Create database dict with available data
                db_data = {}
                for i, col in enumerate(columns_to_select):
                    db_data[col] = row[i]

                # Set defaults for missing columns
                database_entry = {
                    "id": db_data.get("id"),
                    "database_name": db_data.get("database_name"),
                    "expose_in_sqllab": db_data.get("expose_in_sqllab", True),
                    "allow_ctas": db_data.get("allow_ctas", False),
                    "allow_cvas": db_data.get("allow_cvas", False),
                    "allow_dml": db_data.get("allow_dml", False),
                    "allow_run_async": db_data.get("allow_run_async", False),
                    "allow_file_upload": db_data.get("allow_file_upload", False),
                    "allows_subquery": db_data.get("allows_subquery", True),
                    "force_ctas_schema": db_data.get("force_ctas_schema"),
                    "disable_data_preview": db_data.get("disable_data_preview", False),
                    "disable_drill_to_detail": db_data.get(
                        "disable_drill_to_detail", False
                    ),
                    "allow_multi_catalog": db_data.get("allow_multi_catalog", False),
                    "backend": "python_lab",
                }

                databases.append(database_entry)

        return jsonify({"databases": databases}), 200

    except Exception as e:
        logger.error(f"Failed to get databases: {e}")
        return jsonify({"error": "Failed to get databases", "details": str(e)}), 500


@python_lab_bp.route("/schemas", methods=["GET"])
def get_schemas():
    """Get list of schemas for a specific database"""
    try:
        db_id = request.args.get("db_id")
        if not db_id or not db_id.isdigit():
            return jsonify({"error": "Valid db_id parameter is required"}), 400

        # Get database URI
        db_uri = get_database_uri_by_id(int(db_id))
        if not db_uri:
            return jsonify({"error": f"Database with id {db_id} not found"}), 404

        # Connect to target database and get schemas
        from sqlalchemy import create_engine, text

        try:
            engine = create_engine(db_uri)
            with engine.connect() as conn:
                result = conn.execute(
                    text("SELECT schema_name FROM information_schema.schemata")
                )
                schemas = [row[0] for row in result.fetchall()]
        except Exception as e:
            # Fallback for databases that don't support information_schema
            schemas = ["public"]  # Default schema

        return jsonify({"schemas": schemas}), 200

    except Exception as e:
        logger.error(f"Failed to get schemas: {e}")
        return jsonify({"error": "Failed to get schemas", "details": str(e)}), 500


@python_lab_bp.route("/tables", methods=["GET"])
def get_tables():
    """Get list of tables for a specific database and schema"""
    try:
        db_id = request.args.get("db_id")
        schema = request.args.get("schema", "public")

        if not db_id or not db_id.isdigit():
            return jsonify({"error": "Valid db_id parameter is required"}), 400

        # Get database URI
        db_uri = get_database_uri_by_id(int(db_id))
        if not db_uri:
            return jsonify({"error": f"Database with id {db_id} not found"}), 404

        # Get database name for logging
        with superset_connection.cursor() as cursor:
            cursor.execute("SELECT database_name FROM dbs WHERE id = %s", (int(db_id),))
            result = cursor.fetchone()
            db_name = result[0] if result else "Unknown"

        logger.info(f"Getting tables for database: {db_name}, schema: {schema}")

        # Connect to target database and get tables
        from sqlalchemy import create_engine, text

        tables = []
        error_details = None

        try:
            engine = create_engine(db_uri)
            with engine.connect() as conn:
                # Try multiple approaches to get tables

                # Approach 1: information_schema (most databases)
                try:
                    query = text(
                        """
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = :schema AND table_type = 'BASE TABLE'
                        ORDER BY table_name
                    """
                    )
                    result = conn.execute(query, {"schema": schema})
                    tables = [row[0] for row in result.fetchall()]
                    logger.info(
                        f"Found {len(tables)} tables in schema '{schema}' using information_schema"
                    )

                except Exception as e1:
                    logger.warning(f"information_schema approach failed: {e1}")

                    # Approach 2: PostgreSQL specific
                    try:
                        if "postgresql" in db_uri:
                            query = text(
                                """
                                SELECT tablename 
                                FROM pg_tables 
                                WHERE schemaname = :schema
                                ORDER BY tablename
                            """
                            )
                            result = conn.execute(query, {"schema": schema})
                            tables = [row[0] for row in result.fetchall()]
                            logger.info(f"Found {len(tables)} tables using pg_tables")

                    except Exception as e2:
                        logger.warning(f"pg_tables approach failed: {e2}")
                        error_details = f"All approaches failed: {e1}, {e2}"

        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            error_details = f"Database connection failed: {str(e)}"

        response = {
            "tables": tables,
            "count": len(tables),
            "schema": schema,
            "database_id": int(db_id),
            "database_name": db_name,
        }

        if error_details:
            response["debug_info"] = error_details

        if len(tables) == 0:
            response["suggestions"] = [
                "Try a different schema (check /schemas endpoint)",
                "Verify the database has tables",
                "Check database permissions",
            ]

        return jsonify(response), 200

    except Exception as e:
        logger.error(f"Failed to get tables: {e}")
        return jsonify({"error": "Failed to get tables", "details": str(e)}), 500


@python_lab_bp.route("/table_metadata/<int:db_id>/<path:table_name>", methods=["GET"])
def get_table_metadata(db_id: int, table_name: str):
    """Get metadata for a specific table"""
    try:
        schema = request.args.get("schema", "public")

        # Get database URI
        db_uri = get_database_uri_by_id(db_id)
        if not db_uri:
            return jsonify({"error": f"Database with id {db_id} not found"}), 404

        # Connect to target database and get table metadata
        from sqlalchemy import create_engine, text

        try:
            engine = create_engine(db_uri)
            with engine.connect() as conn:
                # Get column information
                query = text(
                    """
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = :table_name AND table_schema = :schema
                    ORDER BY ordinal_position
                """
                )
                result = conn.execute(
                    query, {"table_name": table_name, "schema": schema}
                )

                columns = []
                for row in result.fetchall():
                    columns.append(
                        {
                            "name": row[0],
                            "type": row[1],
                            "nullable": row[2] == "YES",
                            "default": row[3],
                        }
                    )

                # Get row count (with limit for performance)
                try:
                    count_query = text(f"SELECT COUNT(*) FROM {schema}.{table_name}")
                    count_result = conn.execute(count_query)
                    row_count = count_result.fetchone()[0]
                except:
                    row_count = 0  # If count fails, set to 0

        except Exception as e:
            logger.warning(f"Failed to get table metadata: {e}")
            return (
                jsonify({"error": "Failed to get table metadata", "details": str(e)}),
                500,
            )

        metadata = {
            "name": table_name,
            "schema": schema,
            "columns": columns,
            "row_count": row_count,
        }

        return jsonify({"metadata": metadata}), 200

    except Exception as e:
        logger.error(f"Failed to get table metadata: {e}")
        return (
            jsonify({"error": "Failed to get table metadata", "details": str(e)}),
            500,
        )


# ============================================================================
# UTILITY AND DEBUG ENDPOINTS
# ============================================================================


@python_lab_bp.route("/debug/set_password", methods=["POST"])
def set_database_password():
    """Temporarily set the database password for testing"""
    global CORRECT_DB_PASSWORD
    try:
        data = request.json
        if not data or "password" not in data:
            return jsonify({"error": "Password is required in JSON body"}), 400

        CORRECT_DB_PASSWORD = data["password"]
        return (
            jsonify(
                {
                    "success": True,
                    "message": f"Password set successfully",
                    "password_length": len(CORRECT_DB_PASSWORD),
                }
            ),
            200,
        )

    except Exception as e:
        return jsonify({"error": "Failed to set password", "details": str(e)}), 500


@python_lab_bp.route("/debug/test_connection/<int:db_id>", methods=["GET"])
def test_database_connection(db_id: int):
    """Test database connection with different password approaches"""
    try:
        # Get original URI
        with superset_connection.cursor() as cursor:
            cursor.execute(
                "SELECT sqlalchemy_uri, database_name FROM dbs WHERE id = %s", (db_id,)
            )
            result = cursor.fetchone()
            if not result:
                return jsonify({"error": f"Database with id {db_id} not found"}), 404

            original_uri, db_name = result

        test_results = {
            "database_id": db_id,
            "database_name": db_name,
            "has_placeholder": "XXXXXXXXXX" in original_uri,
            "tests": {},
        }

        # Mask URI for response
        masked_uri = original_uri
        if "@" in original_uri and ":" in original_uri:
            parts = original_uri.split("@")
            if len(parts) == 2:
                protocol_user = parts[0]
                if "://" in protocol_user:
                    protocol = protocol_user.split("://")[0]
                    user_part = protocol_user.split("://")[-1]
                    if ":" in user_part:
                        user = user_part.split(":")[0]
                        masked_uri = f"{protocol}://{user}:****@{parts[1]}"

        test_results["masked_original_uri"] = masked_uri

        from sqlalchemy import create_engine

        # Test password replacements
        if "XXXXXXXXXX" in original_uri:
            password_attempts = [
                ("empty", ""),
                ("superset", "superset"),
                ("password", "password"),
                ("admin", "admin"),
                ("postgres", "postgres"),
            ]

            for name, password in password_attempts:
                try:
                    test_uri = original_uri.replace("XXXXXXXXXX", password)
                    engine = create_engine(test_uri)
                    with engine.connect() as conn:
                        conn.execute("SELECT 1")
                        test_results["tests"][f"password_{name}"] = "success"
                        test_results["working_password"] = password
                        break  # Stop on first success
                except Exception as e:
                    test_results["tests"][f"password_{name}"] = f"failed: {str(e)[:50]}"
        else:
            # Test original URI without placeholder
            try:
                engine = create_engine(original_uri)
                with engine.connect() as conn:
                    conn.execute("SELECT 1")
                    test_results["tests"]["original_uri"] = "success"
            except Exception as e:
                test_results["tests"]["original_uri"] = f"failed: {str(e)[:100]}"

        return jsonify(test_results), 200

    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return jsonify({"error": "Connection test failed", "details": str(e)}), 500


@python_lab_bp.route("/debug/superset_connection", methods=["GET"])
def test_superset_connection():
    """Test the Superset database connection itself"""
    try:
        import psycopg2

        # Test different host configurations
        configs_to_try = [
            {"host": "db", "label": "Docker (db host)"},
            {"host": "localhost", "label": "Local (localhost)"},
            {"host": "127.0.0.1", "label": "Local (127.0.0.1)"},
        ]

        results = {}
        working_config = None

        for config_info in configs_to_try:
            host = config_info["host"]
            label = config_info["label"]

            try:
                test_config = {
                    "dbname": "superset",
                    "user": "superset",
                    "password": "superset",
                    "host": host,
                    "port": 5432,
                }

                conn = psycopg2.connect(**test_config)
                with conn.cursor() as cursor:
                    cursor.execute("SELECT COUNT(*) FROM dbs")
                    db_count = cursor.fetchone()[0]
                conn.close()

                results[label] = {
                    "status": "success",
                    "databases_found": db_count,
                    "host": host,
                }

                if not working_config:
                    working_config = test_config

            except Exception as e:
                results[label] = {
                    "status": "failed",
                    "error": str(e)[:100],
                    "host": host,
                }

        response = {
            "connection_tests": results,
            "working_config": working_config,
            "current_global_connection": superset_connection is not None,
        }

        # If we found a working config, update the global function
        if working_config:
            global WORKING_DB_CONFIG
            WORKING_DB_CONFIG = working_config
            response["recommended_action"] = "Use working config found"
        else:
            response["recommended_action"] = (
                "Check database setup - no working connection found"
            )

        return jsonify(response), 200

    except Exception as e:
        return jsonify({"error": "Failed to test connections", "details": str(e)}), 500


# Global variable to store working database config
WORKING_DB_CONFIG = None


def get_superset_db_config():
    """Get Superset database configuration from environment/config"""
    global WORKING_DB_CONFIG

    # If we have a known working config, use it
    if WORKING_DB_CONFIG:
        return WORKING_DB_CONFIG

    import os

    # Check if we're in Docker environment
    is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
    host = "db" if is_docker else "localhost"

    # Try to detect Docker environment automatically
    if not is_docker:
        try:
            # Check if we can connect to 'db' host (Docker)
            import socket

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(("db", 5432))
            sock.close()
            if result == 0:
                host = "db"
        except:
            pass

    return {
        "dbname": "superset",
        "user": "superset",
        "password": "superset",
        "host": host,
        "port": 5432,
    }


@python_lab_bp.route("/debug/check_db/<int:db_id>", methods=["GET"])
def check_database_exists(db_id: int):
    """Simple check if database ID exists"""
    try:
        with superset_connection.cursor() as cursor:
            cursor.execute(
                "SELECT id, database_name, sqlalchemy_uri FROM dbs WHERE id = %s",
                (db_id,),
            )
            result = cursor.fetchone()

            if result:
                db_id_found, db_name, uri = result
                return (
                    jsonify(
                        {
                            "exists": True,
                            "id": db_id_found,
                            "name": db_name,
                            "has_placeholder": "XXXXXXXXXX" in uri,
                            "uri_preview": uri[:50] + "..." if len(uri) > 50 else uri,
                        }
                    ),
                    200,
                )
            else:
                # Get all available IDs
                cursor.execute("SELECT id, database_name FROM dbs ORDER BY id")
                all_dbs = cursor.fetchall()

                return (
                    jsonify(
                        {
                            "exists": False,
                            "requested_id": db_id,
                            "available_databases": [
                                {"id": row[0], "name": row[1]} for row in all_dbs
                            ],
                        }
                    ),
                    404,
                )

    except Exception as e:
        return jsonify({"error": "Failed to check database", "details": str(e)}), 500


@python_lab_bp.route("/debug/database_uris", methods=["GET"])
def debug_all_database_uris():
    """Debug endpoint to see all database URIs and their status"""
    try:
        databases = []

        with superset_connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, database_name, sqlalchemy_uri, expose_in_sqllab 
                FROM dbs 
                ORDER BY id
            """
            )

            for row in cursor.fetchall():
                db_id, db_name, original_uri, expose_in_sqllab = row

                # Mask original URI
                masked_original = original_uri
                if "@" in original_uri and ":" in original_uri:
                    parts = original_uri.split("@")
                    if len(parts) == 2:
                        protocol_user = parts[0]
                        if "://" in protocol_user:
                            protocol = protocol_user.split("://")[0]
                            user_part = protocol_user.split("://")[-1]
                            if ":" in user_part:
                                user = user_part.split(":")[0]
                                masked_original = f"{protocol}://{user}:****@{parts[1]}"

                databases.append(
                    {
                        "id": db_id,
                        "name": db_name,
                        "expose_in_sqllab": expose_in_sqllab,
                        "masked_uri": masked_original,
                        "has_placeholder": "XXXXXXXXXX" in original_uri,
                    }
                )

        return (
            jsonify(
                {
                    "databases": databases,
                    "total_count": len(databases),
                    "exposed_count": sum(
                        1 for db in databases if db["expose_in_sqllab"]
                    ),
                }
            ),
            200,
        )

    except Exception as e:
        logger.error(f"Failed to debug database URIs: {e}")
        return (
            jsonify({"error": "Failed to debug database URIs", "details": str(e)}),
            500,
        )


@python_lab_bp.route("/test_basic", methods=["POST"])
def test_basic_execution():
    """Test basic Python execution without database connection"""
    try:
        data = request.json or {}
        python_code = data.get("python", "result = pd.DataFrame({'test': [1, 2, 3]})")

        import pandas as pd
        import numpy as np

        # Simple execution environment
        safe_globals = {"pd": pd, "pandas": pd, "np": np, "numpy": np}

        local_vars = {}

        # Execute code
        exec(python_code, safe_globals, local_vars)

        # Find result
        result_data = None
        for var_name, var_value in local_vars.items():
            if isinstance(var_value, pd.DataFrame):
                result_data = var_value
                break

        if result_data is not None:
            return (
                jsonify(
                    {
                        "success": True,
                        "data": result_data.values.tolist(),
                        "columns": list(result_data.columns),
                        "rows": len(result_data),
                    }
                ),
                200,
            )
        else:
            return (
                jsonify(
                    {
                        "success": True,
                        "message": "Code executed but no DataFrame found",
                        "variables": list(local_vars.keys()),
                    }
                ),
                200,
            )

    except Exception as e:
        return (
            jsonify(
                {"success": False, "error": str(e), "traceback": traceback.format_exc()}
            ),
            500,
        )


@python_lab_bp.route("/clear_results", methods=["POST"])
def clear_results():
    """Clear all stored results for current user"""
    try:
        user_id = get_user_id()

        # Get all result keys for user's queries
        with superset_connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT results_key FROM python_lab_query 
                WHERE user_id = %s AND results_key IS NOT NULL
            """,
                (user_id,),
            )

            user_result_keys = [row[0] for row in cursor.fetchall()]

        # Remove from storage
        cleared_count = 0
        for key in user_result_keys:
            if key in results_storage:
                del results_storage[key]
                cleared_count += 1

        return (
            jsonify(
                {
                    "success": True,
                    "message": f"Cleared {cleared_count} result sets",
                    "cleared_count": cleared_count,
                }
            ),
            200,
        )

    except Exception as e:
        logger.error(f"Failed to clear results: {e}")
        return jsonify({"error": "Failed to clear results", "details": str(e)}), 500


@python_lab_bp.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint for Python Lab"""
    try:
        # Test database connection
        with superset_connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()

        # Check results storage
        storage_count = len(results_storage)

        return (
            jsonify(
                {
                    "status": "healthy",
                    "service": "python_lab",
                    "database": "connected",
                    "results_storage_count": storage_count,
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            200,
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return (
            jsonify(
                {
                    "status": "unhealthy",
                    "service": "python_lab",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


# ============================================================================
# ADDITIONAL QUERY MANAGEMENT ENDPOINTS
# ============================================================================


@python_lab_bp.route("/queries", methods=["GET"])
def get_queries():
    """Get list of Python queries for current user with pagination"""
    try:
        user_id = get_user_id()

        # Validate query parameters
        try:
            limit = min(int(request.args.get("limit", 50)), 200)  # Max 200
            offset = max(int(request.args.get("offset", 0)), 0)
        except ValueError:
            return jsonify({"error": "Invalid limit or offset parameter"}), 400

        status_filter = request.args.get("status")
        if status_filter and status_filter not in [
            "pending",
            "running",
            "success",
            "failed",
            "stopped",
        ]:
            return jsonify({"error": "Invalid status filter"}), 400

        query_sql = """
            SELECT id, client_id, tab_name, python_code, status, progress,
                   rows, created_on, end_time - start_time as execution_time, database_id
            FROM python_lab_query 
            WHERE user_id = %s
        """
        params = [user_id]

        if status_filter:
            query_sql += " AND status = %s"
            params.append(status_filter)

        query_sql += " ORDER BY created_on DESC LIMIT %s OFFSET %s"
        params.extend([limit, offset])

        with superset_connection.cursor() as cursor:
            cursor.execute(query_sql, params)

            columns = [desc[0] for desc in cursor.description]
            queries = []

            for row in cursor.fetchall():
                query_data = dict(zip(columns, row))
                # Format datetime objects and truncate code
                for key, value in query_data.items():
                    if isinstance(value, datetime):
                        query_data[key] = value.isoformat()
                    elif key == "python_code" and value:
                        query_data[key] = (
                            value[:100] + "..." if len(value) > 100 else value
                        )
                queries.append(query_data)

        return jsonify({"success": True, "data": queries, "count": len(queries)}), 200

    except Exception as e:
        logger.error(f"Failed to retrieve queries: {e}")
        return jsonify({"error": "Failed to retrieve queries", "details": str(e)}), 500


@python_lab_bp.route("/get_query", methods=["GET"])
def get_query():
    """Get a saved Python query by ID - Returns Superset API format"""
    try:
        query_id = request.args.get("id")
        if not query_id or not query_id.isdigit():
            return jsonify({"error": "Valid query ID is required"}), 400

        user_id = get_user_id()

        with superset_connection.cursor() as cursor:
            # Get the saved query with user information and database details
            cursor.execute(
                """
                SELECT 
                    spq.*,
                    u1.first_name as created_by_first_name,
                    u1.last_name as created_by_last_name,
                    u2.first_name as changed_by_first_name, 
                    u2.last_name as changed_by_last_name,
                    db.database_name as database_name
                FROM saved_python_query spq
                LEFT JOIN ab_user u1 ON spq.created_by_fk = u1.id
                LEFT JOIN ab_user u2 ON spq.changed_by_fk = u2.id
                LEFT JOIN dbs db ON spq.db_id = db.id
                WHERE spq.id = %s AND spq.user_id = %s
            """,
                (int(query_id), user_id),
            )

            result = cursor.fetchone()
            if not result:
                return jsonify({"error": f"Query with ID {query_id} not found"}), 404

            # Map the result to variables
            (
                created_on,
                changed_on,
                spq_id,
                spq_user_id,
                db_id,
                label,
                schema,
                python_code,
                description,
                changed_by_fk,
                created_by_fk,
                extra_json,
                last_run,
                rows,
                uuid,
                template_parameters,
                catalog,
                created_by_first_name,
                created_by_last_name,
                changed_by_first_name,
                changed_by_last_name,
                database_name,
            ) = result

        # Calculate humanized time difference
        from datetime import datetime

        def humanize_time_delta(dt):
            if not dt:
                return None
            if isinstance(dt, str):
                dt = datetime.fromisoformat(dt.replace("Z", "+00:00"))

            now = datetime.now(dt.tzinfo) if dt.tzinfo else datetime.now()
            delta = now - dt

            if delta.days > 0:
                if delta.days == 1:
                    return "a day ago"
                return f"{delta.days} days ago"
            elif delta.seconds > 3600:
                hours = delta.seconds // 3600
                if hours == 1:
                    return "an hour ago"
                return f"{hours} hours ago"
            elif delta.seconds > 60:
                minutes = delta.seconds // 60
                if minutes == 1:
                    return "a minute ago"
                return f"{minutes} minutes ago"
            else:
                return "just now"

        # Build the response in Superset API format
        response = {
            "description_columns": {},
            "id": spq_id,
            "label_columns": {
                "catalog": "Catalog",
                "changed_by.first_name": "Changed By First Name",
                "changed_by.id": "Changed By Id",
                "changed_by.last_name": "Changed By Last Name",
                "changed_on": "Changed On",
                "changed_on_delta_humanized": "Changed On Delta Humanized",
                "created_by.first_name": "Created By First Name",
                "created_by.id": "Created By Id",
                "created_by.last_name": "Created By Last Name",
                "database.database_name": "Database Database Name",
                "database.id": "Database Id",
                "description": "Description",
                "id": "Id",
                "label": "Label",
                "schema": "Schema",
                "sql": "Python Code",  # Changed from "Sql" to be more appropriate
                "sql_tables": "Sql Tables",
                "template_parameters": "Template Parameters",
            },
            "result": {
                "catalog": catalog,
                "changed_by": {
                    "first_name": changed_by_first_name or "Superset",
                    "id": changed_by_fk,
                    "last_name": changed_by_last_name or "Admin",
                },
                "changed_on": changed_on.isoformat() if changed_on else None,
                "changed_on_delta_humanized": humanize_time_delta(changed_on),
                "created_by": {
                    "first_name": created_by_first_name or "Superset",
                    "id": created_by_fk,
                    "last_name": created_by_last_name or "Admin",
                },
                "database": {"database_name": database_name or "REME", "id": db_id},
                "description": description or "",
                "id": spq_id,
                "label": label or "",
                "schema": schema or "public",
                "sql": python_code or "",  # Python code in the sql field
                "sql_tables": extract_table_names_from_python(python_code),
                "template_parameters": template_parameters,
            },
            "show_columns": [
                "changed_on",
                "changed_on_delta_humanized",
                "changed_by.first_name",
                "changed_by.id",
                "changed_by.last_name",
                "created_by.first_name",
                "created_by.id",
                "created_by.last_name",
                "database.database_name",
                "database.id",
                "description",
                "id",
                "label",
                "catalog",
                "schema",
                "sql",
                "sql_tables",
                "template_parameters",
            ],
            "show_title": "Show Saved Query",
        }

        return jsonify(response), 200

    except Exception as e:
        logger.error(f"Failed to retrieve query: {e}")
        return jsonify({"error": "Failed to retrieve query", "details": str(e)}), 500


# Also add this helper function for parsing SQL tables from Python code
def extract_table_names_from_python(python_code):
    """Extract table names from Python code (basic implementation)"""
    import re

    if not python_code:
        return []

    # Look for read_sql patterns
    sql_patterns = [
        r'read_sql\([\'"]([^\'"]*)[\'"]\s*,',  # pd.read_sql('SQL', ...)
        r'FROM\s+[\'"]?([a-zA-Z_][a-zA-Z0-9_]*)[\'"]?',  # FROM table_name
        r'FROM\s+[\'"]([^\'"]*)[\'"]\s*',  # FROM "quoted_table"
    ]

    tables = set()

    for pattern in sql_patterns:
        matches = re.findall(pattern, python_code, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                tables.update(match)
            else:
                # Extract table names from SQL
                sql_tables = re.findall(
                    r'FROM\s+[\'"]?([a-zA-Z_][a-zA-Z0-9_]*)[\'"]?', match, re.IGNORECASE
                )
                tables.update(sql_tables)

    return list(tables)


# ============================================================================
# ERROR HANDLERS
# ============================================================================


@python_lab_bp.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Python Lab endpoint not found"}), 404


@python_lab_bp.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error in Python Lab: {error}")
    return jsonify({"error": "Internal server error in Python Lab"}), 500


@python_lab_bp.errorhandler(ValidationError)
def validation_error(error):
    return jsonify({"error": "Validation failed", "details": error.messages}), 400


# ============================================================================
# BACKGROUND TASKS
# ============================================================================


def cleanup_old_results():
    """
    Clean up expired results - should be called by a background task
    """
    try:
        cleanup_expired_results()

        # Also clean up database records older than 30 days
        cutoff_date = datetime.now() - timedelta(days=30)
        with superset_connection.cursor() as cursor:
            cursor.execute(
                """
                DELETE FROM python_lab_query 
                WHERE created_on < %s AND status IN ('success', 'failed')
            """,
                (cutoff_date,),
            )

            deleted_count = cursor.rowcount
            superset_connection.commit()

        logger.info(f"Cleaned up {deleted_count} old query records")

    except Exception as e:
        logger.error(f"Failed to cleanup old results: {e}")
        superset_connection.rollback()


@python_lab_bp.route("/debug/list_all_tables/<int:db_id>", methods=["GET"])
def debug_list_all_tables(db_id: int):
    """Debug endpoint to list all tables and schemas in database"""
    try:
        db_uri = get_database_uri_by_id(db_id)
        if not db_uri:
            return jsonify({"error": f"Database {db_id} not found"}), 404

        from sqlalchemy import create_engine, text

        engine = create_engine(db_uri)

        debug_info = {
            "database_id": db_id,
            "current_database": None,
            "schemas": [],
            "tables_by_schema": {},
            "total_tables": 0,
        }

        with engine.connect() as conn:
            # Get current database name
            try:
                result = conn.execute(text("SELECT current_database()"))
                debug_info["current_database"] = result.fetchone()[0]
            except:
                pass

            # Get all schemas
            try:
                result = conn.execute(
                    text(
                        """
                    SELECT schema_name 
                    FROM information_schema.schemata 
                    WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
                    ORDER BY schema_name
                """
                    )
                )
                debug_info["schemas"] = [row[0] for row in result.fetchall()]
            except Exception as e:
                debug_info["schemas_error"] = str(e)

            # Get all tables grouped by schema
            try:
                result = conn.execute(
                    text(
                        """
                    SELECT table_schema, table_name, table_type
                    FROM information_schema.tables 
                    WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
                    ORDER BY table_schema, table_name
                """
                    )
                )

                for row in result.fetchall():
                    schema, table, table_type = row
                    if schema not in debug_info["tables_by_schema"]:
                        debug_info["tables_by_schema"][schema] = []
                    debug_info["tables_by_schema"][schema].append(
                        {"name": table, "type": table_type}
                    )
                    debug_info["total_tables"] += 1

            except Exception as e:
                debug_info["tables_error"] = str(e)

            # Look specifically for competencies table (case variations)
            competencies_search = []
            try:
                search_queries = [
                    "SELECT table_schema, table_name FROM information_schema.tables WHERE LOWER(table_name) LIKE '%competenc%'",
                    "SELECT table_schema, table_name FROM information_schema.tables WHERE table_name ILIKE '%competenc%'",
                ]

                for query in search_queries:
                    try:
                        result = conn.execute(text(query))
                        for row in result.fetchall():
                            competencies_search.append(
                                {"schema": row[0], "table": row[1]}
                            )
                    except:
                        continue

            except Exception as e:
                debug_info["competencies_search_error"] = str(e)

            debug_info["competencies_matches"] = competencies_search

        return jsonify(debug_info), 200

    except Exception as e:
        logger.error(f"Failed to debug tables: {e}")
        return jsonify({"error": str(e), "traceback": traceback.format_exc()}), 500


@python_lab_bp.route("/debug/create_sample_table/<int:db_id>", methods=["POST"])
def debug_create_sample_table(db_id: int):
    """Create a sample competencies table for testing"""
    try:
        db_uri = get_database_uri_by_id(db_id)
        if not db_uri:
            return jsonify({"error": f"Database {db_id} not found"}), 404

        from sqlalchemy import create_engine, text
        import pandas as pd

        engine = create_engine(db_uri)

        # Create sample data
        sample_data = pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": [
                    "Leadership",
                    "Communication",
                    "Problem Solving",
                    "Teamwork",
                    "Technical Skills",
                ],
                "level": ["Advanced", "Intermediate", "Expert", "Beginner", "Advanced"],
                "category": ["Soft", "Soft", "Soft", "Soft", "Technical"],
                "description": [
                    "Ability to guide and inspire teams",
                    "Effective verbal and written communication",
                    "Analytical thinking and solution finding",
                    "Collaborative work skills",
                    "Domain-specific technical expertise",
                ],
            }
        )

        # Save to database
        sample_data.to_sql("competencies", engine, if_exists="replace", index=False)

        return (
            jsonify(
                {
                    "success": True,
                    "message": "Sample competencies table created successfully",
                    "rows_created": len(sample_data),
                    "table_name": "competencies",
                    "database_id": db_id,
                }
            ),
            201,
        )

    except Exception as e:
        logger.error(f"Failed to create sample table: {e}")
        return jsonify({"error": str(e), "traceback": traceback.format_exc()}), 500


@python_lab_bp.route("/debug/test_query/<int:db_id>", methods=["POST"])
def debug_test_query(db_id: int):
    """Test various queries to debug table access issues"""
    try:
        data = request.json or {}
        table_name = data.get("table_name", "competencies")
        schema = data.get("schema", "public")

        db_uri = get_database_uri_by_id(db_id)
        if not db_uri:
            return jsonify({"error": f"Database {db_id} not found"}), 404

        from sqlalchemy import create_engine, text
        import pandas as pd

        engine = create_engine(db_uri)
        test_results = {}

        with engine.connect() as conn:
            # Test 1: Basic connection
            try:
                result = conn.execute(text("SELECT 1 as test"))
                test_results["basic_connection"] = "SUCCESS"
            except Exception as e:
                test_results["basic_connection"] = f"FAILED: {str(e)}"

            # Test 2: Current database
            try:
                result = conn.execute(text("SELECT current_database()"))
                test_results["current_database"] = result.fetchone()[0]
            except Exception as e:
                test_results["current_database"] = f"FAILED: {str(e)}"

            # Test 3: Table exists check
            table_variations = [
                table_name.lower(),
                table_name.upper(),
                table_name.capitalize(),
                f'"{table_name}"',
                f"{schema}.{table_name}",
                f'"{schema}"."{table_name}"',
            ]

            test_results["table_existence_tests"] = {}

            for variation in table_variations:
                try:
                    query = text(
                        f"""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_name = '{variation.replace('"', '').lower()}'
                            AND table_schema = '{schema}'
                        )
                    """
                    )
                    result = conn.execute(query)
                    exists = result.fetchone()[0]
                    test_results["table_existence_tests"][variation] = exists
                except Exception as e:
                    test_results["table_existence_tests"][
                        variation
                    ] = f"ERROR: {str(e)}"

            # Test 4: Try to query the table with different variations
            test_results["query_tests"] = {}

            for variation in table_variations:
                try:
                    if "public." in variation or '"' in variation:
                        query = text(f"SELECT COUNT(*) FROM {variation}")
                    else:
                        query = text(f"SELECT COUNT(*) FROM {variation}")

                    result = conn.execute(query)
                    count = result.fetchone()[0]
                    test_results["query_tests"][variation] = f"SUCCESS: {count} rows"
                except Exception as e:
                    test_results["query_tests"][variation] = f"FAILED: {str(e)[:100]}"

        return (
            jsonify(
                {
                    "database_id": db_id,
                    "table_name": table_name,
                    "schema": schema,
                    "test_results": test_results,
                }
            ),
            200,
        )

    except Exception as e:
        logger.error(f"Failed to test queries: {e}")
        return jsonify({"error": str(e), "traceback": traceback.format_exc()}), 500
