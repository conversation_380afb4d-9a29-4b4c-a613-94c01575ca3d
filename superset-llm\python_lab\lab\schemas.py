from marshmallow import Schema, fields, validate, post_load, ValidationError
from typing import Dict, Any




class EstimatePythonCostSchema(Schema):
    """Schema for Python code cost estimation requests"""

    database_id = fields.Integer(allow_none=True, validate=validate.Range(min=1))
    database_name = fields.String(
        allow_none=True, validate=validate.Length(min=1, max=255)
    )
    python = fields.String(required=True, validate=validate.Length(min=1, max=10000))
    template_params = fields.Dict(missing=dict)
    catalog = fields.String(allow_none=True)
    schema = fields.String(allow_none=True)

    def validate_database(self, data, **kwargs):
        """Ensure either database_id or database_name is provided"""
        if not data.get("database_id") and not data.get("database_name"):
            raise ValidationError(
                "Either database_id or database_name must be provided"
            )


class ExecutePythonPayloadSchema(Schema):
    """Schema for Python code execution requests"""

    database_id = fields.Integer(allow_none=True, validate=validate.Range(min=1))
    database_name = fields.String(
        allow_none=True, validate=validate.Length(min=1, max=255)
    )
    python = fields.String(required=True, validate=validate.Length(min=1, max=10000))
    catalog = fields.String(allow_none=True)
    schema = fields.String(allow_none=True, validate=validate.Length(max=128))
    template_params = fields.Dict(missing=dict)
    runAsync = fields.Boolean(missing=False)
    queryLimit = fields.Integer(
        missing=1000, validate=validate.Range(min=1, max=1000000)
    )
    status = fields.String(missing="pending")
    select_as_cta = fields.Boolean(missing=False)
    ctas_method = fields.String(
        missing="TABLE", validate=validate.OneOf(["TABLE", "VIEW"])
    )
    tmp_table_name = fields.String(allow_none=True, validate=validate.Length(max=256))
    tmp_schema_name = fields.String(allow_none=True, validate=validate.Length(max=128))
    client_id = fields.String(allow_none=True)
    python_editor_id = fields.String(allow_none=True)
    tab = fields.String(allow_none=True, validate=validate.Length(max=256))
    expand_data = fields.Boolean(missing=False)

    def validate_database(self, data, **kwargs):
        """Ensure either database_id or database_name is provided"""
        if not data.get("database_id") and not data.get("database_name"):
            raise ValidationError(
                "Either database_id or database_name must be provided"
            )


class FormatPythonPayloadSchema(Schema):
    """Schema for Python code formatting requests"""

    python = fields.String(required=True, validate=validate.Length(min=1, max=10000))
    engine = fields.String(missing="python")


class ValidatePythonPayloadSchema(Schema):
    """Schema for Python code validation requests"""

    python = fields.String(required=True, validate=validate.Length(min=1, max=10000))


class SavePythonQuerySchema(Schema):
    """Schema for saving Python queries"""

    catalog = fields.String(allow_none=True)
    db_id = fields.Integer(required=True, validate=validate.Range(min=1))
    description = fields.String(missing="", validate=validate.Length(max=1000))
    label = fields.String(required=True, validate=validate.Length(min=1, max=256))
    python = fields.String(required=True, validate=validate.Length(min=1, max=10000))


class SaveDatasetSchema(Schema):
    """Schema for saving execution results as datasets"""

    python = fields.String(required=True, validate=validate.Length(min=1, max=10000))
    db_id = fields.Integer(required=True, validate=validate.Range(min=1))
    catalog = fields.String(allow_none=True)
    schema = fields.String(allow_none=True, validate=validate.Length(max=128))
    table_name = fields.String(required=True, validate=validate.Length(min=1, max=256))
    if_exists = fields.String(
        missing="fail", validate=validate.OneOf(["fail", "replace", "append"])
    )


class PythonQueryExecutionResponseSchema(Schema):
    """Schema for Python query execution responses"""

    query = fields.Dict(required=True)
    data = fields.List(fields.List(fields.Raw()), missing=list)
    success = fields.Boolean(required=True)
    displayLimitReached = fields.Boolean(missing=False)
    error = fields.String(allow_none=True)
    stacktrace = fields.String(allow_none=True)


class PythonLabBootstrapSchema(Schema):
    """Schema for Python Lab bootstrap data response"""

    active_tab = fields.Dict(allow_none=True)
    databases = fields.Dict(required=True)
    queries = fields.List(fields.Dict(), missing=list)
    tab_state_ids = fields.List(fields.Dict(), missing=list)


class PythonQuerySchema(Schema):
    """Schema for Python query objects"""

    id = fields.Integer(allow_none=True)
    client_id = fields.String(allow_none=True)
    user_id = fields.Integer(allow_none=True)
    database_id = fields.Integer(allow_none=True)
    status = fields.String(missing="pending")
    tab_name = fields.String(allow_none=True)
    python_code = fields.String(allow_none=True)
    catalog = fields.String(allow_none=True)
    schema = fields.String(allow_none=True)
    limit = fields.Integer(missing=1000)
    select_as_cta = fields.Boolean(missing=False)
    ctas_method = fields.String(allow_none=True)
    tmp_table_name = fields.String(allow_none=True)
    tmp_schema_name = fields.String(allow_none=True)
    start_time = fields.Float(allow_none=True)
    end_time = fields.Float(allow_none=True)
    progress = fields.Integer(missing=0)
    rows = fields.Integer(missing=0)
    error_message = fields.String(allow_none=True)
    results_key = fields.String(allow_none=True)
    python_editor_id = fields.String(allow_none=True)
    template_params = fields.Dict(missing=dict)
    created_on = fields.DateTime(allow_none=True)
    changed_on = fields.DateTime(allow_none=True)
    created_by_fk = fields.Integer(allow_none=True)
    changed_by_fk = fields.Integer(allow_none=True)


class PythonLabGetResultsSchema(Schema):
    """Schema for getting Python Lab results"""

    key = fields.String(required=True)
    rows = fields.Integer(allow_none=True, validate=validate.Range(min=1, max=100000))


class PythonValidationAnnotationSchema(Schema):
    """Schema for Python validation annotations"""

    line_number = fields.Integer(allow_none=True)
    start_column = fields.Integer(allow_none=True)
    end_column = fields.Integer(allow_none=True)
    message = fields.String(required=True)
    severity = fields.String(
        missing="error", validate=validate.OneOf(["error", "warning", "info"])
    )


class PythonCostEstimationSchema(Schema):
    """Schema for Python cost estimation responses"""

    complexity_score = fields.Integer(required=True)
    estimated_time = fields.String(required=True)
    lines_of_code = fields.Integer(required=True)


class DatabaseSchema(Schema):
    """Schema for database objects in Python Lab"""

    id = fields.Integer(required=True)
    database_name = fields.String(required=True)
    expose_in_sqllab = fields.Boolean(required=True)
    allow_ctas = fields.Boolean(required=True)
    allow_cvas = fields.Boolean(required=True)
    allow_dml = fields.Boolean(required=True)
    allow_run_async = fields.Boolean(required=True)
    allow_file_upload = fields.Boolean(missing=False)
    allows_subquery = fields.Boolean(missing=True)
    force_ctas_schema = fields.String(allow_none=True)
    disable_data_preview = fields.Boolean(missing=False)
    disable_drill_to_detail = fields.Boolean(missing=False)
    allow_multi_catalog = fields.Boolean(missing=False)
    backend = fields.String(missing="python_lab")


class TabStateSchema(Schema):
    """Schema for Python Lab tab state"""

    id = fields.Integer(allow_none=True)
    label = fields.String(required=True)
    python_code = fields.String(allow_none=True)
    database_id = fields.Integer(allow_none=True)
    catalog = fields.String(allow_none=True)
    schema = fields.String(allow_none=True)
    autorun = fields.Boolean(missing=False)
    template_params = fields.String(missing="{}")
    latest_query_id = fields.Integer(allow_none=True)
    active = fields.Boolean(missing=False)


class ErrorResponseSchema(Schema):
    """Schema for error responses"""

    error = fields.String(required=True)
    details = fields.String(allow_none=True)
    error_type = fields.String(allow_none=True)


class SuccessResponseSchema(Schema):
    """Schema for success responses"""

    success = fields.Boolean(required=True)
    message = fields.String(allow_none=True)
    data = fields.Raw(allow_none=True)


# Schema instances for use in API
estimate_python_cost_schema = EstimatePythonCostSchema()
execute_python_payload_schema = ExecutePythonPayloadSchema()
format_python_payload_schema = FormatPythonPayloadSchema()
validate_python_payload_schema = ValidatePythonPayloadSchema()
save_python_query_schema = SavePythonQuerySchema()
save_dataset_schema = SaveDatasetSchema()
python_query_execution_response_schema = PythonQueryExecutionResponseSchema()
python_lab_bootstrap_schema = PythonLabBootstrapSchema()
python_query_schema = PythonQuerySchema()
python_lab_get_results_schema = PythonLabGetResultsSchema()
python_validation_annotation_schema = PythonValidationAnnotationSchema()
python_cost_estimation_schema = PythonCostEstimationSchema()
database_schema = DatabaseSchema()
tab_state_schema = TabStateSchema()
error_response_schema = ErrorResponseSchema()
success_response_schema = SuccessResponseSchema()
